import 'package:diacritic/diacritic.dart';
import 'package:soma_core/modules/catalog/models/product/product.dart';
import 'package:soma_core/modules/checkout/models/orderForm/table_measures.dart';
import 'package:soma_ui/tokens/structure/sizes_table_theme.dart';
import 'package:dio/dio.dart';
import 'package:off_app_flutter/config/off_config.dart';

class OffSizesTable implements SizesTableFactory {
  const OffSizesTable();

  @override
  bool hasSizesTable(Product product) {
    return false;
  }

  @override
  Future<SizesTable> forProductAsync(Product product,
      {TableMeasures? tableMeasures}) async {
    final dio = Dio();
    final tamanho = product.items?[0].tamanho?[0];
    String? foundCategory;
    bool existsInListaTamanhosLetras = false;
    final String brandSlug = removeDiacritics(product.brand ?? '')
        .toLowerCase()
        .replaceAll(' ', '_');

    final List<String> targetWords = ['feminino', 'masculino', 'infantil'];
    final List<String> targetWordsLower = ['PP', 'P', 'M', 'G', 'GG', '3G'];

    final categoriesReversed = product.categories?.reversed;
    if (categoriesReversed != null) {
      for (final rawCategory in categoriesReversed) {
        final lowerCategory = rawCategory.toLowerCase();

        for (final targetWord in targetWords) {
          if (lowerCategory.contains(targetWord)) {
            foundCategory = targetWord;
            break;
          }
        }

        if (foundCategory != null) break;
      }
    }

    final endpoint =
        'https://somabus-kong.somalabs.com.br/api/dataentity/GM/search?_where=brand=$brandSlug  AND type=$foundCategory &_limit=100&_fields=bodyParts,brand,measure,order,size,type&range=resources=0-150';

    final List<BodyMeasure> bodyMeasuresList = [];
    List<SizeMeasures> sizesMeasuresFromListaTamanhosLetras = [];
    List<SizeMeasures> sizesMeasuresFromListaTamanhosNumeros = [];

    try {
      final response = await dio.get(
        endpoint,
        options: Options(
          headers: {
            'posSoma': productionPosSoma.posSoma,
          },
        ),
      );

      final List<dynamic> data = response.data;

      final List<dynamic> listaTamanhosLetras = data
          .where((item) {
            return targetWordsLower.contains(item['size']);
          })
          .toSet()
          .toList()
        ..sort((a, b) {
          final sizes = targetWordsLower;
          return sizes.indexOf(a['size']) - sizes.indexOf(b['size']);
        });

      listaTamanhosLetras.removeWhere((item) => item['size'] == null);

      final List<dynamic> listaTamanhosNumeros = data.where((item) {
        final String? size = item['size']?.toString();
        return size != null && !targetWordsLower.contains(size);
      }).toList()
        ..sort((a, b) {
          final int aSize = int.tryParse(a['size'].toString()) ?? 0;
          final int bSize = int.tryParse(b['size'].toString()) ?? 0;
          return aSize.compareTo(bSize);
        });

      listaTamanhosNumeros.removeWhere((item) => item['size'] == null);

      final Set<String> uniqueBodyParts = {};
      Map<String, Map<int, dynamic>> measuresGroupedBySize = {};

      existsInListaTamanhosLetras =
          listaTamanhosLetras.any((item) => item['size'] == tamanho);

      for (var item in existsInListaTamanhosLetras
          ? listaTamanhosLetras
          : listaTamanhosNumeros) {
        final String bodyPartName = item['bodyParts'];
        if (!uniqueBodyParts.contains(bodyPartName)) {
          uniqueBodyParts.add(bodyPartName);
          bodyMeasuresList.add(BodyMeasure(
            id: uniqueBodyParts.length,
            name: bodyPartName,
          ));
        }
      }

      if (existsInListaTamanhosLetras && uniqueBodyParts.isNotEmpty) {
        for (var item in listaTamanhosLetras) {
          String tamanho = item['size'].toString();
          if (!measuresGroupedBySize.containsKey(tamanho)) {
            measuresGroupedBySize[tamanho] = {};
          }
          for (var bodyMeasure in bodyMeasuresList) {
            if (bodyMeasure.name == item['bodyParts'] &&
                item['measure'] != null) {
              measuresGroupedBySize[tamanho]?[bodyMeasure.id] =
                  item['measure'].toString();
            }
          }
        }
        sizesMeasuresFromListaTamanhosLetras =
            measuresGroupedBySize.entries.map((entry) {
          return SizeMeasures(
            size: entry.key,
            measures: entry.value,
          );
        }).toList();

        return SizesTable(
          bodyMeasures: bodyMeasuresList,
          sizeMeasures: sizesMeasuresFromListaTamanhosLetras,
          type: SizesTableType.async,
        );
      }
      if (!existsInListaTamanhosLetras && uniqueBodyParts.isNotEmpty) {
        for (var item in listaTamanhosNumeros) {
          final String tamanho = item['size'].toString();

          if (!measuresGroupedBySize.containsKey(tamanho)) {
            measuresGroupedBySize[tamanho] = {};
          }

          for (var bodyMeasure in bodyMeasuresList) {
            final String? partName = item['bodyParts']?.toString();
            final String? measure = item['measure']?.toString();

            if (bodyMeasure.name == partName && measure != null) {
              measuresGroupedBySize[tamanho]?[bodyMeasure.id] = measure;
            }
          }
        }
        sizesMeasuresFromListaTamanhosNumeros =
            measuresGroupedBySize.entries.map((entry) {
          return SizeMeasures(
            size: entry.key,
            measures: entry.value,
          );
        }).toList();
        return SizesTable(
          bodyMeasures: bodyMeasuresList,
          sizeMeasures: sizesMeasuresFromListaTamanhosNumeros,
          type: SizesTableType.async,
        );
      }
    } catch (e, stackTrace) {
      print('StackTrace: $stackTrace');
    }

    return const SizesTable(
      bodyMeasures: [],
      sizeMeasures: [],
      type: SizesTableType.simple,
    );
  }

  @override
  SizesTable forProduct(Product product, {TableMeasures? tableMeasures}) {
    final List<BodyMeasure> bodyMeasuresList = [];
    final List<String> bodyMeasureStringList = [];
    final List<SizeMeasures> sizeMeasuresList = [];

    for (Map size in product.sizeTable) {
      Map<int, dynamic> measures = {};
      if (size.values.first.isNotEmpty) {
        var sizesMensures = size.values.first.split(',');

        for (int index = 0; index < sizesMensures.length; index++) {
          var sizeTypeValues = sizesMensures[index].split("=");

          String measure = sizeTypeValues.first.trim();

          var hasMeasureIndex = bodyMeasureStringList.indexOf(measure);

          if (hasMeasureIndex == -1) {
            bodyMeasureStringList.add(measure);
            measures
                .addAll({index: double.parse(sizeTypeValues.last.toString())});
          } else {
            measures.addAll({
              hasMeasureIndex: double.parse(sizeTypeValues.last.toString())
            });
          }
        }

        sizeMeasuresList
            .add(SizeMeasures(size: size.keys.first, measures: measures));
      }
    }

    bodyMeasureStringList.asMap().forEach(
          (index, measureType) => bodyMeasuresList.add(
            BodyMeasure(id: index, name: measureType),
          ),
        );

    return SizesTable(
      bodyMeasures: bodyMeasuresList,
      sizeMeasures: sizeMeasuresList,
    );
  }
}
