import 'package:flutter/material.dart';
import 'package:soma_core/modules/checkout/models/orderForm/table_measures.dart';
import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:diacritic/diacritic.dart';

class SMBottomSheetSizeTable extends StatefulWidget {
  final Product product;
  final String? selectedSize;
  final TableMeasures? measures;

  const SMBottomSheetSizeTable({
    super.key,
    required this.product,
    this.measures,
    this.selectedSize,
  });

  @override
  State<SMBottomSheetSizeTable> createState() => _SMBottomSheetSizeTableState();
}

class _SMBottomSheetSizeTableState extends State<SMBottomSheetSizeTable>
    with DesignTokensStateMixin {
  @override
  Widget build(BuildContext context) {
    final sizesTable = sizesTableTheme.sizesTable.forProduct(widget.product);
    final title = textTransform.title(
      sizesTable.type == SizesTableType.jewelry
          ? '<PERSON><PERSON><PERSON> de tamanho'
          : termsAndMessages.sizesTableBottomSheetTitle,
      capitalizeFirstOnly: false,
    );
    final subtitle = termsAndMessages.sizesTableBottomSheetSubtitle != null
        ? textTransform
            .body(termsAndMessages.sizesTableBottomSheetSubtitle!.interpolate({
            'nameOfProduct': widget.product.productName ?? '',
          }))
        : null;

    final brandName = removeDiacritics(widget.product.brand ?? '')
        .toLowerCase()
        .replaceAll(' ', '_');

    ImageProvider? brandImage;
    if (brandName != '' && sizesTableTheme.brandImages != null) {
      brandImage = sizesTableTheme.brandImages![brandName];
    }

    return (bottomSheetSizeTableTheme.hasEmptyBottomSheetSizeTable &&
            sizesTable.sizeMeasures.isEmpty &&
            sizesTable.bodyMeasures.isEmpty &&
            widget.measures == null)
        ? SMBottomSheet(
            title: termsAndMessages.emptyBottomSheetSizeTableTitle,
            subtitle: termsAndMessages.emptyBottomSheetSizeTableSubtitle,
            onTapClose: () => Navigator.pop(context),
            onTap: () => Navigator.pop(context),
            hideButton: false,
            buttonTitle: termsAndMessages.emptyBottomSheetSizeTableButtonText,
            buttonTextColor: colors.typography.pure1,
            buttonBackgroundColor: colors.neutral.pure2,
            child: _buildEmptyContent(),
          )
        : SMBottomSheet(
            title: bottomSheetSizeTableTheme.hasScrollableHeader ? null : title,
            subtitle:
                bottomSheetSizeTableTheme.hasScrollableHeader ? null : subtitle,
            childHeightFactor:
                bottomSheetSizeTableTheme.bottomSheetHeightFactor,
            padding: bottomSheetSizeTableTheme.padding,
            bottom: bottomSheetSizeTableTheme.bottom,
            onTapClose: () => Navigator.pop(context),
            hideCloseButton: sizesTableTheme.hideCloseButton ?? true,
            showDragHandling: sizesTableTheme.showDragHandling ?? false,
            hideButton: sizesTableTheme.hideButton ?? true,
            fixedTitle: sizesTableTheme.sizesTableBottomSheetHeaderTitle,
            fixedTitleStyle: typography.typeStyles.bodyMd,
            child: _buildContent(
              bottomSheetSizeTableTheme,
              measures: widget.measures,
              title:
                  sizesTableTheme.sizesTableBottomSheetHeaderSubtitle ?? title,
              subtitle: subtitle,
              brandImage: brandImage,
            ),
          );
  }

  Widget _buildEmptyContent() {
    return const SizedBox();
  }

  Widget _buildContent(
    BottomSheetSizeTableTheme theme, {
    TableMeasures? measures,
    required String title,
    required String? subtitle,
    ImageProvider? brandImage,
  }) {
    final sizesTable = sizesTableTheme.sizesTable
        .forProduct(widget.product, tableMeasures: widget.measures);

    final hideTopInfo = sizesTable.type == SizesTableType.jewelry &&
        tokens.sizesTableTheme.hideTitleWhenJewelryImage;

    return NestedScrollView(
      headerSliverBuilder: (_, __) => [
        SliverToBoxAdapter(
          child: Column(
            children: [
              if (theme.hasScrollableHeader && !hideTopInfo) ...[
                if (brandImage != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(
                        top: 0, bottom: 0), //apenas a imagem
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: SizedBox(
                        height: 50,
                        child: Image(
                          image: brandImage,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ],
                Padding(
                  padding: theme.headerPadding ?? const EdgeInsets.all(0),
                  child: SMMainContent(
                    title: title,
                    titleStyle: typography.typeStyles.bodySm,
                    subtitle: subtitle,
                    subtitleStyle: typography.typeStyles.bodyXs,
                  ),
                ),
              ],
              if (theme.hasHeaderDivider && !hideTopInfo)
                SMLine(
                  lineSize: LineSize.small,
                  lineColor: colors.neutral.light2,
                ),
            ],
          ),
        ),
      ],
      body: SMSizesTable(
        product: widget.product,
        selectedSize: widget.selectedSize,
        padding: theme.hasHeaderDivider
            ? EdgeInsets.only(top: spacingStack.lg)
            : null,
        measures: measures,
      ),
    );
  }
}
