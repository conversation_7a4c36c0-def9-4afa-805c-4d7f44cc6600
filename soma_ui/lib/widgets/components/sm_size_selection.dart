import 'dart:async';

import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:soma_core/modules/catalog/models/product/item.dart';

import 'package:soma_core/soma_core.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/widgets/templates/pdp/widgets/index.dart';
import 'package:super_tooltip/super_tooltip.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SMSizeSelection extends StatefulWidget {
  final Product product;
  final List<Item> itemsList;
  final Item? selectedSize;
  final VoidCallback onTapTableSizes;
  final VoidCallback onTapMySizeIsOver;
  final void Function(String?) onTapWarnMe;
  final ValueChanged<Item> onSelectSize;
  final Color? availableBackgroundColor;

  const SMSizeSelection(
      {super.key,
      required this.product,
      required this.itemsList,
      required this.onTapTableSizes,
      required this.onTapMySizeIsOver,
      required this.onSelectSize,
      required this.onTapWarnMe,
      this.selectedSize,
      this.availableBackgroundColor});

  @override
  State<SMSizeSelection> createState() => _SMSizeSelectionState();
}

class _SMSizeSelectionState extends State<SMSizeSelection>
    with
        DesignTokensStateMixin,
        SomaCoreStateMixin,
        AnalyticsEventDispatcherStateMixin {
  bool get _hasUnavailableSizes {
    return widget.itemsList.any((i) => !i.isAvailable);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _SizeSectionTitle(),
        _ProductAvailableSizes(
          product: widget.product,
          onSelectSize: widget.onSelectSize,
          selectedSize: widget.selectedSize,
        ),
        if (widget.selectedSize != null &&
            !widget.selectedSize!.isAvailable &&
            warnMeTheme.showWarnMe)
          _ProductReplacementNotifyCard(
            selectedSize: widget.selectedSize,
            onTapWarnMe: widget.onTapWarnMe,
          ),
        _ProductSizesFurtherInformation(
          product: widget.product,
          hasUnavailableSizes: _hasUnavailableSizes,
          onTapMySizeIsOver: widget.onTapMySizeIsOver,
          onTapTableSizes: widget.onTapTableSizes,
        ),
      ],
    );
  }
}

class _SizeSectionTitle extends StatelessWidget {
  const _SizeSectionTitle();

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final pdpTheme = tokens.pdpTheme;
    return Padding(
      padding: EdgeInsets.only(bottom: tokens.spacingStack.md),
      child: Text(
        tokens.textTransform.body('tamanhos'),
        style: pdpTheme.sectionsTitleStyle ??
            tokens.typography.typeStyles.subtitle,
      ),
    );
  }
}

class _ProductAvailableSizes extends StatelessWidget {
  const _ProductAvailableSizes({
    required this.product,
    required this.selectedSize,
    required this.onSelectSize,
  });

  final Product product;
  final Item? selectedSize;
  final ValueChanged<Item> onSelectSize;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final warnMeTheme = tokens.warnMeTheme;
    List<Item> itemsList = product.items ?? [];
    final intelligentSearchConfig =
        context.locateService<Config>().intelligentSearchConfig;
    final config = context.locateService<Config>();
    if (intelligentSearchConfig.reorderAscFilters &&
        intelligentSearchConfig.sizeOrder != null) {
      itemsList = Item.reorderItensBySizeBrandOrder(
          product.items ?? [], intelligentSearchConfig.sizeOrder!);
    }
    return SizedBox(
      height: tokens.spacingStack.xul,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: itemsList.length,
        separatorBuilder: (_, __) => SizedBox(width: tokens.spacingInline.xs),
        itemBuilder: (_, index) {
          final size = itemsList[index];
          final isSizeAvailable = size.firstAvailableSeller?.commertialOffer
                  ?.isAvailableAndWithoutError ==
              true;

          final showAvailabilityNotice =
              size.hasOne || (!isSizeAvailable && warnMeTheme.showWarnMe);
          final isProductFromPartnerSeller =
              config.appFeaturesConfig.isEnablePartnerSellerTag &&
                  !brandWithPartnerSellersEnabled
                      .contains(size.firstAvailableSeller?.sellerName);

          return Column(
            children: [
              isProductFromPartnerSeller
                  ? _ProductSizeButtonWithTooltip(
                      size: size,
                      onSelectSize: onSelectSize,
                      selectedSize: selectedSize,
                      isSizeAvailable: isSizeAvailable,
                      isProductFromPartnerSeller: isProductFromPartnerSeller,
                      quantityItems: product.items?.length,
                    )
                  : _ProductSizeButton(
                      size: size,
                      onSelectSize: onSelectSize,
                      selectedSize: selectedSize,
                      isSizeAvailable: isSizeAvailable,
                    ),
              if (showAvailabilityNotice) _ProductSizeAvailabilityNotice(size),
            ].separated(SizedBox(height: tokens.spacingStack.xs)),
          );
        },
      ),
    );
  }
}

class _ProductSizeButton extends StatelessWidget {
  const _ProductSizeButton({
    required this.size,
    required this.selectedSize,
    required this.onSelectSize,
    required this.isSizeAvailable,
  });

  final Item size;
  final Item? selectedSize;
  final ValueChanged<Item> onSelectSize;
  final bool isSizeAvailable;

  @override
  Widget build(BuildContext context) {
    final analytics = context.locateService<AnalyticsEventDispatcher>();
    final tokens = context.designTokens;

    return SMSizeButton(
      textVariant: TextVariant.upperCase,
      text: TextUtils.verifySizeText(size.itemSize),
      height: tokens.spacingStack.xl,
      backgroundColor: !isSizeAvailable ? tokens.colors.neutral.medium1 : null,
      textColor: !isSizeAvailable ? tokens.colors.typography.light2 : null,
      isActive: size.itemId == selectedSize?.itemId && isSizeAvailable,
      isCrossed: !isSizeAvailable,
      onTap: () {
        analytics.dispatchSelectContentEvent(
          'sm-size-selection:${size.itemSize}',
        );
        onSelectSize(size);
      },
    );
  }
}

class _ProductSizeButtonWithTooltip extends StatefulWidget {
  const _ProductSizeButtonWithTooltip({
    required this.size,
    required this.selectedSize,
    required this.onSelectSize,
    required this.isSizeAvailable,
    this.isProductFromPartnerSeller = false,
    this.quantityItems,
  });

  final Item size;
  final Item? selectedSize;
  final ValueChanged<Item> onSelectSize;
  final bool isSizeAvailable;
  final bool isProductFromPartnerSeller;
  final int? quantityItems;

  @override
  State<_ProductSizeButtonWithTooltip> createState() =>
      _ProductSizeButtonWithTooltipState();
}

class _ProductSizeButtonWithTooltipState
    extends State<_ProductSizeButtonWithTooltip>
    with DesignTokensStateMixin, AnalyticsEventDispatcherStateMixin {
  final _superTooltipController = SuperTooltipController();
  final productSizeKey = GlobalKey();
  bool isInit = true;

  void showTooltip() async {
    if (!widget.isSizeAvailable) return;
    await _superTooltipController.showTooltip().then(
          (value) => Future.delayed(const Duration(seconds: 10), () async {
            if (mounted) {
              await _superTooltipController.hideTooltip();
            }
          }),
        );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isUniqueSize =
        widget.selectedSize?.itemSize == "U" && widget.quantityItems == 1;

    return VisibilityDetector(
      key: productSizeKey,
      onVisibilityChanged: (info) {
        if (isInit && info.visibleBounds.bottom > 38) {
          isInit = false;
          if (widget.isProductFromPartnerSeller && isUniqueSize) {
            Future.delayed(const Duration(milliseconds: 1500), () async {
              if (mounted) {
                showTooltip();
              }
            });
          }
        }
      },
      child: SuperTooltip(
        backgroundColor: Colors.white,
        popupDirection:
            isUniqueSize ? TooltipDirection.right : TooltipDirection.up,
        arrowTipDistance: 25,
        arrowBaseWidth: 16,
        arrowLength: 8,
        barrierColor: Colors.transparent,
        borderColor: Colors.transparent,
        borderRadius: 16,
        showBarrier: false,
        boxShadows: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.45),
            spreadRadius: 0.8,
            blurRadius: 15,
          )
        ],
        left: isUniqueSize ? null : (screenWidth * 0.15),
        controller: _superTooltipController,
        showCloseButton: false,
        content: TapRegion(
          onTapOutside: (_) async {
            await _superTooltipController.hideTooltip();
          },
          child: Container(
            height: isUniqueSize ? 112 : 100,
            width: 295,
            padding: EdgeInsets.only(
              top: isUniqueSize ? 12 : 4,
              left: spacingInline.xxs,
              right: spacingInline.xxs,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: isUniqueSize ? 200 : 215,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        termsAndMessages.partnerSellerDefaultDescription ?? '',
                        style: typeStyles.body.copyWith(
                          color: colors.typography.pure2,
                        ),
                      ),
                      const SizedBox(height: 12),
                      InkWell(
                        onTap: () {
                          _superTooltipController.hideTooltip();
                          customShowBottomSheet(
                              const BottomSheetPartnerSellerTag());
                        },
                        child: Text(
                          'saiba mais',
                          style: typography.typeStyles.bodySm
                              .copyWith(decoration: TextDecoration.underline),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: spacingInline.xs),
                GestureDetector(
                  onTap: () {
                    _superTooltipController.hideTooltip();
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Icon(
                      icons.close,
                      size: 16.0,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        child: SMSizeButton(
          textVariant: TextVariant.upperCase,
          text: TextUtils.verifySizeText(widget.size.itemSize),
          height: spacingStack.xl,
          backgroundColor:
              !widget.isSizeAvailable ? colors.neutral.medium1 : null,
          textColor: !widget.isSizeAvailable ? colors.typography.light2 : null,
          isActive: widget.size.itemId == widget.selectedSize?.itemId &&
              widget.isSizeAvailable,
          isCrossed: !widget.isSizeAvailable,
          onTap: () {
            analyticsEventDispatcher.dispatchSelectContentEvent(
              'sm-size-selection:${widget.size.itemSize}',
            );
            showTooltip();
            widget.onSelectSize(widget.size);
          },
        ),
      ),
    );
  }
}

class _ProductSizeAvailabilityNotice extends StatelessWidget {
  const _ProductSizeAvailabilityNotice(this.size);

  final Item size;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final infoErrorMessage =
        size.sellers?.firstOrNull?.commertialOffer?.getInfoErrorMessage;
    return Text(
      tokens.textTransform.body(
        size.hasOne && infoErrorMessage == null
            ? tokens.termsAndMessages.lastProductMessage
            : tokens.termsAndMessages.productOutOfStock,
      ),
      style: tokens.typography.typeStyles.bodyCaption.copyWith(
        color: tokens.colors.feedback.pureError,
      ),
    );
  }
}

class _ProductReplacementNotifyCard extends StatelessWidget {
  const _ProductReplacementNotifyCard({
    required this.selectedSize,
    required this.onTapWarnMe,
  });

  final Item? selectedSize;
  final void Function(String?) onTapWarnMe;

  @override
  Widget build(BuildContext context) {
    final analytics = context.locateService<AnalyticsEventDispatcher>();
    final tokens = context.designTokens;
    return Padding(
      padding: EdgeInsets.only(bottom: tokens.spacingStack.lg),
      child: Column(
        children: [
          SizedBox(height: tokens.spacingStack.md),
          Container(
            padding: EdgeInsets.all(tokens.spacingStack.md),
            decoration: BoxDecoration(
              color: tokens.colors.neutral.light1,
              borderRadius:
                  BorderRadius.circular(tokens.borderRadius.radiusMedium),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _ReplacementNotifyCardPresentation(selectedSize: selectedSize),
                SMButton.secondary(
                  child: Text(
                    tokens.textTransform.body('avise-me quando chegar'),
                  ),
                  size: ButtonSize.small,
                  expanded: true,
                  onPressed: () {
                    analytics.dispatchLogNotifyMeEvent();
                    onTapWarnMe(selectedSize?.itemId);
                  },
                ),
              ].separated(SizedBox(height: tokens.spacingStack.sm)),
            ),
          ),
        ],
      ),
    );
  }
}

class _ReplacementNotifyCardPresentation extends StatelessWidget {
  const _ReplacementNotifyCardPresentation({required this.selectedSize});

  final Item? selectedSize;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tokens.termsAndMessages.wishlistSoldOutSizeMessage.interpolate({
            'size': "${selectedSize?.tamanho?.firstOrNull?.toUpperCase()}",
            'smile': tokens.termsAndMessages.sadSmile
          }),
          style: tokens.typography.typeStyles.bodySm,
        ),
        Text(
          tokens.textTransform.body(tokens.termsAndMessages.warnMeText ?? ''),
          style: tokens.typography.typeStyles.body,
        ),
      ].separated(SizedBox(height: tokens.spacingStack.xs)),
    );
  }
}

class _ProductSizesFurtherInformation extends StatelessWidget {
  const _ProductSizesFurtherInformation({
    required this.product,
    required this.hasUnavailableSizes,
    required this.onTapTableSizes,
    required this.onTapMySizeIsOver,
  });

  final Product product;
  final bool hasUnavailableSizes;
  final VoidCallback onTapTableSizes;
  final VoidCallback onTapMySizeIsOver;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final pdpTheme = tokens.pdpTheme;
    final sizesTableTheme = tokens.sizesTableTheme;
    final asyncTable = sizesTableTheme.iSasyncSizesTable;

    if (asyncTable &&
        pdpTheme.belowSelectSizeActions
            .contains(BelowSelectSizeActions.measurements)) {
      return FutureBuilder<bool>(
        future: _checkIfHasValidSizesTable(sizesTableTheme, product),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildOtherButtons(context, tokens, pdpTheme);
          }

          final hasValidSizesTable = snapshot.data ?? false;
          return _buildAllButtons(
              context, tokens, pdpTheme, hasValidSizesTable);
        },
      );
    }

    // Para tabelas síncronas, usa a lógica original
    final productHasSizesTable =
        sizesTableTheme.sizesTable.hasSizesTable(product);
    final showTableSizesButton = pdpTheme.belowSelectSizeActions
            .contains(BelowSelectSizeActions.measurements) &&
        productHasSizesTable;
    return _buildAllButtons(context, tokens, pdpTheme, showTableSizesButton);
  }

  Future<bool> _checkIfHasValidSizesTable(
      SizesTableTheme sizesTableTheme, Product product) async {
    try {
      final sizesTable =
          await sizesTableTheme.sizesTable.forProductAsync(product);
      return sizesTable != null &&
          sizesTable.bodyMeasures.isNotEmpty &&
          sizesTable.sizeMeasures.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  Widget _buildAllButtons(BuildContext context, DesignTokens tokens,
      PdpTheme pdpTheme, bool showTableSizesButton) {
    final showJewelryStoneGuide = pdpTheme.jewelryStoneGuideWidget != null &&
        pdpTheme.isJewelry != null &&
        pdpTheme.isJewelry!(product);
    final showSizeOutOfStockButton = pdpTheme.belowSelectSizeActions
            .contains(BelowSelectSizeActions.outOfStock) &&
        hasUnavailableSizes;
    final showFindSimilarsButton = pdpTheme.belowSelectSizeActions
        .contains(BelowSelectSizeActions.findSimilars);

    return Wrap(
      spacing: tokens.spacingInline.sm,
      children: [
        if (showTableSizesButton)
          _BelowProductSizesButton(
            title: tokens.termsAndMessages.sizesTableBottomSheetTitle,
            onTap: onTapTableSizes,
          ),
        if (showJewelryStoneGuide) pdpTheme.jewelryStoneGuideWidget!,
        if (showSizeOutOfStockButton)
          _BelowProductSizesButton(
            title: tokens.termsAndMessages.missingSize,
            onTap: onTapMySizeIsOver,
          ),
        if (showFindSimilarsButton)
          _BelowProductSizesButton(
            title: 'encontrar similares',
            onTap: () {
              // TODO: conectar VERTAPP-1485 [NV] Encontrar similares
            },
          )
      ],
    );
  }

  Widget _buildOtherButtons(
      BuildContext context, DesignTokens tokens, PdpTheme pdpTheme) {
    return _buildAllButtons(context, tokens, pdpTheme, false);
  }
}

class _BelowProductSizesButton extends StatelessWidget {
  const _BelowProductSizesButton({required this.title, required this.onTap});

  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final bottomSheetTheme = context.designTokens.bottomSheetSizeTableTheme;
    final isExpanded = bottomSheetTheme.expandedButton ?? false;

    return Container(
      margin: isExpanded
          ? EdgeInsets.only(top: tokens.spacingInset.lg)
          : EdgeInsets.zero,
      height: tokens.spacingStack.xl,
      child: SMButton.link(
        onPressed: onTap,
        expanded: isExpanded,
        child: Text(
          tokens.textTransform.body(title),
          style:
              bottomSheetTheme.textStyle ?? tokens.typography.typeStyles.bodySm,
        ),
        trailing:
            Icon(tokens.icons.arrowRight, size: bottomSheetTheme.arrowIconSize),
        style: bottomSheetTheme.buttonStyle ??
            SMButtonStyle(
              padding: EdgeInsets.symmetric(vertical: tokens.spacingStack.xs),
            ),
      ),
    );
  }
}

abstract class PpdMeasureTablePlugin implements Plugin {
  Widget build(BuildContext context, Product product);
}
