import 'package:flutter/material.dart';
import 'package:soma_core/soma_core.dart';
import '../../soma_ui.dart';

class SMAsyncSizesTable extends StatelessWidget {
  final SizesTable sizesTable;
  final double? tableHeaderHeight;
  final SizesTableTheme theme;
  final Product? product;

  const SMAsyncSizesTable({
    super.key,
    required this.sizesTable,
    this.tableHeaderHeight,
    required this.theme,
    this.product,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    return sizesTable.sizeMeasures.isEmpty == true
        ? Padding(
            padding: EdgeInsets.only(right: tokens.spacingInset.xxl),
            child: const SMMainContent(
              subtitle: '<PERSON><PERSON><PERSON><PERSON>, ainda não temos as medidas para esta peça.',
            ),
          )
        : _buildTableContent(context);
  }

  Widget _buildTableContent(BuildContext context) {
    final tokens = context.designTokens;
    final List<String> targetWords = ['feminino', 'masculino', 'infantil'];
    String? foundCategory;
    final routes = context.locateService<AppRoutes>();
    final config = context.locateService<Config>();
    final categoriesReversed = product?.categories?.reversed;

    if (categoriesReversed != null) {
      for (final rawCategory in categoriesReversed) {
        final lowerCategory = rawCategory.toLowerCase();
        for (final targetWord in targetWords) {
          if (lowerCategory.contains(targetWord)) {
            foundCategory = targetWord;
            break;
          }
        }
        if (foundCategory != null) break;
      }
    }

    return Column(
      children: [
        _SizesTable(
          bodyMeasures: sizesTable.bodyMeasures,
          sizeMeasures: sizesTable.sizeMeasures,
          headerHeight: tableHeaderHeight,
        ),
        SizedBox(height: tokens.spacingStack.sm),
        SizedBox(
          width: double.infinity,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'ARRASTE PARA O LADO PARA VER TUDO',
                style: tokens.typography.typeStyles.bodyCaption.copyWith(
                  color: tokens.colors.typography.light2,
                ),
              ),
              SizedBox(width: tokens.spacingInline.xxs),
              Icon(tokens.icons.arrowRight,
                  size: tokens.bottomSheetSizeTableTheme.arrowIconSize,
                  color: tokens.colors.typography.light2),
              Transform.translate(
                offset: const Offset(-10, 0),
                child: Icon(tokens.icons.arrowRight,
                    size: tokens.bottomSheetSizeTableTheme.arrowIconSize,
                    color: tokens.colors.typography.light2),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
              top: tokens.spacingStack.lg, bottom: tokens.spacingStack.lg),
          child: const SMLine(
            lineSize: LineSize.small,
            lineColor: null,
          ),
        ),
        SMMainContent(
          title: theme.tableImageTitle ?? 'Tabela de medidas',
          titleStyle: tokens.typography.typeStyles.bodySm,
          subtitle: theme.tableImageSubTitle,
          subtitleStyle: tokens.typography.typeStyles.bodyXs,
        ),
        Align(
          alignment: Alignment.centerLeft,
          child: (foundCategory != null &&
                  theme.measuresImages?[foundCategory] != null)
              ? Padding(
                  padding: EdgeInsets.only(
                      top: tokens.spacingStack.md,
                      bottom: tokens.spacingStack.md),
                  child: SMImage(image: theme.measuresImages![foundCategory]!),
                )
              : Padding(
                  padding: EdgeInsets.only(
                      top: tokens.spacingStack.md,
                      bottom: tokens.spacingStack.md),
                  child: SMImage(image: theme.measuresImages!['feminino']!),
                ),
        ),
        _MeasuringInstructions(
          theme: theme,
          product: product,
          foundCategory: foundCategory,
        ),
        Padding(
          padding: EdgeInsets.only(
              top: tokens.spacingStack.lg, bottom: tokens.spacingStack.lg),
          child: const SMLine(
            lineSize: LineSize.small,
            lineColor: null,
          ),
        ),
        if (theme.hasNeedHelpContainer)
          Container(
            color: null,
            padding: null,
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'PRECISA DE AJUDA?',
                      style: tokens.bottomSheetSizeTableTheme.textStyle ??
                          tokens.typography.typeStyles.bodySm,
                    )
                  ],
                ),
                SizedBox(height: tokens.spacingStack.xs),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () => navigateToNamed(
                        routes.webview,
                        arguments: WebViewParams(
                          url: config.appUrls.faq ?? '',
                          screenName: 'FAQ',
                        ),
                      ),
                      child: Text(
                        'Perguntas Frequentes',
                        style: tokens.bottomSheetSizeTableTheme.textStyle
                                ?.copyWith(
                              decoration: TextDecoration.underline,
                            ) ??
                            tokens.typography.typeStyles.bodySm.copyWith(
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        if (theme.hasNeedHelpContainer)
          Container(
              padding: EdgeInsets.all(tokens.spacingInset.md),
              height: 67,
              margin: EdgeInsets.only(top: tokens.spacingStack.xl),
              width: MediaQuery.of(context).size.width + 100,
              color: tokens.colors.neutral.light1,
              child: Row(
                children: [
                  Icon(tokens.icons.faq,
                      size: tokens.spacingStack.md,
                      color: tokens.colors.brand.pure1),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Precisou trocar?\nA devolução é grátis.',
                          style: tokens.typography.typeStyles.bodyXs,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        GestureDetector(
                          onTap: () {
                            customShowBottomSheet(
                              SMBottomSheet(
                                title: tokens.textTransform.title(tokens
                                    .termsAndMessages.productExchangeOrReturn),
                                onTapClose: () => navigateBack(),
                                hideButton: true,
                                child: SingleChildScrollView(
                                  child: Text(
                                    tokens.textTransform.body(tokens
                                        .termsAndMessages
                                        .productExchangeOrReturnDescription),
                                  ),
                                ),
                              ),
                            );
                          },
                          child: Text(
                            'Confira os detalhes aqui',
                            style:
                                tokens.typography.typeStyles.bodyXs?.copyWith(
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )),
      ],
    );
  }
}

class _SizesTable extends StatefulWidget {
  final List<BodyMeasure> bodyMeasures;
  final List<SizeMeasures?> sizeMeasures;
  final double? headerHeight;

  const _SizesTable({
    required this.bodyMeasures,
    required this.sizeMeasures,
    this.headerHeight,
  });

  @override
  State<_SizesTable> createState() => _SizesTableState();
}

class _SizesTableState extends State<_SizesTable> with DesignTokensStateMixin {
  @override
  Widget build(BuildContext context) {
    String rowName(index, indexBodyMeasure) {
      final measure = widget.sizeMeasures[index]!
          .measures[widget.bodyMeasures[indexBodyMeasure].id];
      if (measure is num) {
        return '${measure.toInt()}';
      } else {
        return '$measure';
      }
    }

    return DefaultTextStyle(
      style: typography.typeStyles.body.copyWith(
        color: colors.typography.pure2,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          child: Table(
            border: TableBorder.symmetric(
              inside: BorderSide.none,
              outside: BorderSide.none,
            ),
            columnWidths: {
              0: const FixedColumnWidth(180),
              for (int i = 1; i <= widget.sizeMeasures.length; i++)
                i: const FixedColumnWidth(90),
            },
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            children: [
              _buildTableHeader(),
              for (int indexBodyMeasure = 0;
                  indexBodyMeasure < widget.bodyMeasures.length;
                  indexBodyMeasure++)
                TableRow(
                  decoration: BoxDecoration(
                    color: indexBodyMeasure % 2 == 0
                        ? colors.neutral.light1
                        : colors.neutral.medium1,
                    border: const Border.symmetric(
                      horizontal: BorderSide.none,
                      vertical: BorderSide.none,
                    ),
                  ),
                  children: [
                    _buildCell(
                      height: tokens.spacingStack.xxl,
                      hasPaddingRight: false,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            widget.bodyMeasures[indexBodyMeasure].name,
                            style:
                                tokens.typography.typeStyles.bodyXs?.copyWith(
                              color: colors.typography.pure2,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          if (widget.bodyMeasures[indexBodyMeasure]
                                      .measuringInstructions !=
                                  null &&
                              widget.bodyMeasures[indexBodyMeasure]
                                  .measuringInstructions!.isNotEmpty)
                            Text(
                              widget.bodyMeasures[indexBodyMeasure]
                                  .measuringInstructions!,
                              style:
                                  tokens.typography.typeStyles.bodyXs?.copyWith(
                                color: colors.typography.light2,
                              ),
                            ),
                        ],
                      ),
                      textAlignment: Alignment.centerLeft,
                      hasPaddingLeft: true,
                    ),
                    for (int i = 0; i < widget.sizeMeasures.length; i++)
                      _buildCell(
                        Text(
                          rowName(i, indexBodyMeasure).toUpperCase(),
                          style: tokens.typography.typeStyles.bodyXs?.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        height: tokens.spacingStack.xl,
                      ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  TableRow _buildTableHeader() {
    return TableRow(
      decoration: BoxDecoration(
        color: colors.neutral.medium1,
        border: const Border.symmetric(
          horizontal: BorderSide.none,
          vertical: BorderSide.none,
        ),
      ),
      children: [
        _buildCell(
          Text('MEDIDAS DO CORPO (CM)',
              style: typeStyles.bodyXs?.copyWith(
                fontWeight: FontWeight.w700,
              )),
          height: widget.headerHeight,
          textAlignment: Alignment.centerLeft,
          hasPaddingLeft: true,
        ),
        for (int i = 0; i < widget.sizeMeasures.length; i++)
          _buildCell(
            Text(
              widget.sizeMeasures[i]?.size ?? '',
              style: typeStyles.bodyXs?.copyWith(
                color: colors.typography.pure2,
                fontWeight: FontWeight.w700,
              ),
            ),
            height: widget.headerHeight,
          ),
      ],
    );
  }

  Widget _buildCell(
    Widget content, {
    Alignment textAlignment = Alignment.center,
    Color? color,
    double? height,
    double? width,
    bool hasPaddingRight = false,
    bool hasPaddingLeft = false,
  }) {
    return Container(
      padding: EdgeInsets.only(
          right: hasPaddingRight ? spacingInline.xxs : 0,
          left: hasPaddingLeft ? spacingInline.sm : 0),
      width: width,
      height: height,
      color: color ?? Colors.transparent,
      alignment: textAlignment,
      child: content,
    );
  }
}

class _MeasuringInstructions extends StatelessWidget {
  final SizesTableTheme theme;
  final Product? product;
  final String? foundCategory;

  const _MeasuringInstructions({
    required this.theme,
    this.product,
    this.foundCategory,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final titleStyle = tokens.typography.typeStyles.bodyXs;

    final bodyStyle = tokens.typography.typeStyles.bodyXs;

    List<BodyMeasure> selectedBodyMeasures;
    switch (foundCategory) {
      case 'infantil':
        selectedBodyMeasures = theme.bodyMeasuresInstructionsKids ??
            defaultBodyMeasuresInstructionsKids;
        break;
      case 'masculino':
        selectedBodyMeasures = theme.bodyMeasuresInstructionsMen ??
            defaultBodyMeasuresInstructionsMen;
        break;
      case 'feminino':
      default:
        selectedBodyMeasures =
            theme.bodyMeasuresInstructions ?? defaultBodyMeasuresInstructions;
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < selectedBodyMeasures.length; i++) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              theme.roundedNumerationStyle
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          height: tokens.spacingStack.md,
                          width: tokens.spacingStack.md,
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(
                              tokens.spacingStack.md,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              '${i + 1}',
                              style: titleStyle?.copyWith(color: Colors.white),
                            ),
                          ),
                        ),
                        SizedBox(width: tokens.spacingStack.sm),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(selectedBodyMeasures[i].name,
                                  style: titleStyle),
                              SizedBox(
                                height: tokens.spacingStack.xxs,
                              ),
                              Text(
                                selectedBodyMeasures[i].measuringInstructions ??
                                    '',
                                style: bodyStyle,
                              )
                            ],
                          ),
                        ),
                      ],
                    )
                  : Text(
                      '${i + 1}. ${selectedBodyMeasures[i].name.toUpperCase()}',
                      style: titleStyle),
              SizedBox(height: tokens.spacingStack.xxs),
              if (!theme.roundedNumerationStyle)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 0,
                  ),
                  child: Text(
                    selectedBodyMeasures[i].measuringInstructions ?? '',
                    style: bodyStyle,
                  ),
                ),
            ],
          ),
          if (i < selectedBodyMeasures.length - 1)
            SizedBox(height: tokens.spacingStack.md),
        ],
        SizedBox(height: tokens.spacingStack.sm),
      ],
    );
  }
}
