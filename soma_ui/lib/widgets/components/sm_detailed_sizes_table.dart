import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:soma_core/soma_core.dart';

import '../../soma_ui.dart';

class SMDetailedSizesTable extends StatefulWidget {
  final SizesTable sizesTable;
  final ImageProvider bodyMeasuresImage;
  final String? initialSelectedSize;
  final double? tableRowHeight;
  final double? tableHeaderHeight;
  final SizesTableTheme theme;
  final Product? product;

  const SMDetailedSizesTable({
    super.key,
    required this.sizesTable,
    required this.bodyMeasuresImage,
    required this.theme,
    this.product,
    this.initialSelectedSize,
    this.tableRowHeight,
    this.tableHeaderHeight,
  });

  @override
  State<SMDetailedSizesTable> createState() => _SMDetailedSizesTableState();
}

class _SMDetailedSizesTableState extends State<SMDetailedSizesTable>
    with DesignTokensStateMixin {
  String? _selectedSize;
  List<String?> _selectedSizes = [];
  List<SizeMeasures?> _selectedSizesMeasures = [];
  Map<int, dynamic>? _selectedSizeMeasures;
  bool? _isSizeMeasuresEmpty;

  @override
  void initState() {
    super.initState();

    _isSizeMeasuresEmpty = widget.sizesTable.sizeMeasures.isEmpty;
    if (_isSizeMeasuresEmpty == false) {
      _selectSize(widget.initialSelectedSize);
    }
  }

  void _selectMultipleSizes(String? size) {
    if (size != null && !_selectedSizes.contains(size)) {
      setState(() {
        _selectedSizes.add(size);
      });
    } else {
      setState(() {
        _selectedSizes.remove(size);
      });
    }

    List<SizeMeasures> availableSelectedSize = widget.sizesTable.sizeMeasures
        .where((element) => _selectedSizes.contains(element.size))
        .toList();

    setState(() {
      _selectedSizesMeasures = availableSelectedSize;
      _selectedSizes = availableSelectedSize.map((e) => e.size).toList();
    });
  }

  void _selectSize(String? size) {
    SizeMeasures availableSelectedSize = widget.sizesTable.sizeMeasures
            .firstWhereOrNull((s) => s.size == size) ??
        widget.sizesTable.sizeMeasures.first;

    setState(() {
      _selectedSize = availableSelectedSize.size;
      _selectedSizeMeasures = availableSelectedSize.measures;
    });
  }

  @override
  Widget build(BuildContext context) {
    return _isSizeMeasuresEmpty == true
        ? Padding(
            padding: EdgeInsets.only(right: spacingInset.xxl),
            child: const SMMainContent(
              subtitle: 'Desculpe, ainda não temos as medidas para esta peça.',
            ),
          )
        : _buildTableContent();
  }

  Widget _buildTableContent() {
    final theme = widget.theme;
    final analytics = context.locateService<AnalyticsEventDispatcher>();
    final item = ProductDetailsItem(
      title: tokens.textTransform
          .title(tokens.termsAndMessages.productExchangeOrReturn),
      description: Text(
        tokens.textTransform
            .body(tokens.termsAndMessages.productExchangeOrReturnDescription),
      ),
    );
    return Column(
      children: [
        SMMainContent(
          title: theme.tableTitle ?? 'Tabela de medidas',
          subtitle: theme.tableSubTitle ??
              'Selecione um tamanho abaixo para checar suas medidas na tabela.',
        ),
        SizedBox(
          height: spacingStack.lg - spacingStack.md,
        ),
        theme.hasMultipleSizeSelection
            ? _MultipleSizeSelector(
                sizes:
                    widget.sizesTable.sizeMeasures.map((s) => s.size).toList(),
                onSelect: _selectMultipleSizes,
                selected: _selectedSizes,
              )
            : _SizeSelector(
                sizes:
                    widget.sizesTable.sizeMeasures.map((s) => s.size).toList(),
                onSelect: _selectSize,
                selected: _selectedSize,
              ),
        if (_selectedSizeMeasures != null &&
            _selectedSize != null &&
            !theme.hasMultipleSizeSelection) ...[
          SizedBox(height: spacingStack.lg),
          _SizesTable(
            bodyMeasures: widget.sizesTable.bodyMeasures,
            measures: _selectedSizeMeasures!,
            selectedSize: _selectedSize!,
            rowHeight: widget.tableRowHeight,
            headerHeight: widget.tableHeaderHeight,
          ),
        ],
        if (theme.hasMultipleSizeSelection) ...[
          SizedBox(height: spacingStack.lg),
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(tokens.icons.arrowLeft),
                  const Text('Arraste a tabela para ver mais'),
                  Icon(tokens.icons.arrowRight),
                ],
              ),
              SizedBox(height: spacingStack.xs),
              _MultipeSizesTable(
                bodyMeasures: widget.sizesTable.bodyMeasures,
                selectedSizeMeasures: _selectedSizesMeasures.isEmpty
                    ? widget.sizesTable.sizeMeasures
                    : _selectedSizesMeasures,
                selectedSizes: _selectedSizes.isEmpty
                    ? widget.sizesTable.sizeMeasures.map((e) => e.size).toList()
                    : _selectedSizes,
                rowHeight: widget.tableRowHeight,
                headerHeight: widget.tableHeaderHeight,
              ),
            ],
          ),
        ],
        SizedBox(height: spacingStack.xxxl),
        theme.tableImageTitle != null && theme.tableImageSubTitle != null
            ? SMMainContent(
                title: theme.tableImageTitle,
                subtitle: theme.tableImageSubTitle ??
                    'Selecione um tamanho abaixo para checar suas medidas na tabela.',
              )
            : SMLine(
                lineSize: LineSize.small, lineColor: colors.neutral.light2),
        SizedBox(height: spacingStack.xxxl),
        SMImage(image: widget.bodyMeasuresImage),
        SizedBox(height: spacingStack.ul),
        _MeasuringInstructions(
          bodyMeasures: widget.sizesTable.measuringInstructions ??
              widget.sizesTable.bodyMeasures,
          theme: theme,
        ),
        if (theme.hasExchangeContainer)
          Container(
            color: tokens.colors.neutral.light1,
            padding: EdgeInsets.all(spacingStack.xs),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Troca ou devolução',
                  style: tokens.typography.typeStyles.body.copyWith(
                    fontWeight: FontWeight.w600,
                    color: tokens.colors.typography.pure2,
                  ),
                ),
                SizedBox(height: spacingStack.xs),
                Text(
                  'Se ainda assim não servir, você pode devolver o produto gratuitamente em até ${tokens.termsAndMessages.devolutionDays} dias.',
                  style: tokens.typography.typeStyles.body.copyWith(
                    color: tokens.colors.typography.pure2,
                  ),
                ),
                SizedBox(height: spacingStack.xs),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    analytics.dispatchSelectContentEvent(
                      item.title,
                      product: widget.product,
                    );
                    analytics.analyticsController.logProductDetailsEvent(
                      product: widget.product,
                      interaction: item.title,
                      screenClass: 'AboutTheProduct',
                    );
                    customShowBottomSheet(
                      Container(
                        constraints: item.bottomSheetHeightFactor != null
                            ? BoxConstraints.tightFor(
                                height: MediaQuery.of(context).size.height *
                                    item.bottomSheetHeightFactor!,
                              )
                            : null,
                        child: AnalyticsMetadataProvider(
                          metadata: {
                            'product': widget.product,
                          },
                          child: SMBottomSheet(
                            title: item.title,
                            onTapClose: () => navigateBack(),
                            hideButton: true,
                            child: item.description,
                          ),
                        ),
                      ),
                    );
                  },
                  child: Text(
                    'Saber mais',
                    style: tokens.typography.typeStyles.bodySm.copyWith(
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        if (theme.hasExchangeContainer)
          SizedBox(height: tokens.spacingStack.sm),
        if (theme.hasExchangeContainer)
          Container(
            color: tokens.colors.neutral.light1,
            padding: EdgeInsets.all(spacingStack.xs),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Precisa de ajuda?',
                  style: tokens.typography.typeStyles.body.copyWith(
                    fontWeight: FontWeight.w600,
                    color: tokens.colors.typography.pure2,
                  ),
                ),
                SizedBox(height: spacingStack.xs),
                Text(
                  'Não encontrei meu tamanho. Qual a melhor recomendação?',
                  style: tokens.typography.typeStyles.body.copyWith(
                    color: tokens.colors.typography.pure2,
                  ),
                ),
                SizedBox(height: spacingStack.xs),
                GestureDetector(
                  onTap: () {
                    final redirectUtils =
                        context.locateService<RedirectUtils>();
                    redirectUtils.launchWhatsapp();
                  },
                  child: Text(
                    'Falar com personal shopper',
                    style: tokens.typography.typeStyles.bodySm.copyWith(
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        SizedBox(height: spacingStack.xl),
      ],
    );
  }
}

class _SizeSelector extends StatelessWidget {
  final List<String> sizes;
  final ValueChanged<String> onSelect;
  final String? selected;

  const _SizeSelector({
    required this.sizes,
    required this.onSelect,
    this.selected,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    return GridView.extent(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      maxCrossAxisExtent: 56,
      mainAxisSpacing: tokens.spacingSquish.sm,
      crossAxisSpacing: tokens.spacingSquish.sm,
      children: [
        for (final size in sizes)
          SMButton.secondary(
            onPressed: () => onSelect(size),
            child: Text(
              tokens.textTransform.button(size),
              style: TextStyle(
                fontWeight: FontWeight.w600,
                height: 1,
                color: selected == size
                    ? tokens.colors.neutral.pure1
                    : tokens.colors.neutral.pure2,
              ),
            ),
            style: SMButtonStyle(
              padding: EdgeInsets.zero,
              borderColor: tokens.colors.neutral.pure2,
              backgroundColor:
                  selected == size ? tokens.colors.neutral.pure2 : null,
            ),
          ),
      ],
    );
  }
}

class _SizesTable extends StatefulWidget {
  final List<BodyMeasure> bodyMeasures;
  final Map<int, dynamic> measures;
  final String selectedSize;
  final double? rowHeight;
  final double? headerHeight;

  const _SizesTable({
    required this.bodyMeasures,
    required this.measures,
    required this.selectedSize,
    this.rowHeight,
    this.headerHeight,
  });

  @override
  State<_SizesTable> createState() => _SizesTableState();
}

class _SizesTableState extends State<_SizesTable> with DesignTokensStateMixin {
  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      style: typography.typeStyles.body.copyWith(
        color: colors.typography.pure2,
        fontWeight: FontWeight.w500,
      ),
      child: Table(
        border: TableBorder.all(),
        columnWidths: const {1: FixedColumnWidth(100)},
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: [
          _buildTableHeader(),
          for (int i = 0; i < widget.bodyMeasures.length; i++)
            TableRow(
              children: [
                _buildCell(
                  Row(
                    children: [
                      Text('${i + 1}. '),
                      Expanded(
                        child: Text(
                          widget.bodyMeasures[i].nameOnSizesTable,
                          softWrap: false,
                        ),
                      ),
                    ],
                  ),
                  textAlignment: Alignment.centerLeft,
                ),
                _buildCell(
                  Text('${widget.measures[widget.bodyMeasures[i].id]} cm'),
                  color: colors.neutral.light1,
                  cellAlignment: TableCellVerticalAlignment.fill,
                ),
              ],
            ),
        ],
      ),
    );
  }

  TableRow _buildTableHeader() {
    return TableRow(
      children: [
        _buildCell(
          Text('Medidas de corpo',
              style: typeStyles.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              )),
          textAlignment: Alignment.centerLeft,
          height: widget.headerHeight,
        ),
        _buildCell(
          Text('Tam. ${widget.selectedSize}',
              style: typeStyles.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              )),
          color: colors.neutral.light1,
          height: widget.headerHeight,
        ),
      ],
    );
  }

  Widget _buildCell(
    Widget content, {
    Alignment textAlignment = Alignment.center,
    Color? color,
    TableCellVerticalAlignment? cellAlignment,
    double? height,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
      constraints: BoxConstraints.tightFor(height: height ?? widget.rowHeight),
      color: color ?? Colors.transparent,
      alignment: textAlignment,
      child: content,
    );
  }
}

class _MeasuringInstructions extends StatelessWidget {
  final List<BodyMeasure> bodyMeasures;
  final SizesTableTheme theme;

  const _MeasuringInstructions({
    required this.bodyMeasures,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;
    final titleStyle = tokens.typography.typeStyles.subtitle.copyWith(
      fontWeight: FontWeight.w600,
      color: tokens.colors.typography.pure2,
    );
    final bodyStyle = tokens.typography.typeStyles.body.copyWith(
      color: tokens.colors.typography.pure2,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < bodyMeasures.length; i++) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              theme.roundedNumerationStyle
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          height: tokens.spacingStack.md,
                          width: tokens.spacingStack.md,
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(
                              tokens.spacingStack.md,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              '${i + 1}',
                              style: titleStyle.copyWith(color: Colors.white),
                            ),
                          ),
                        ),
                        SizedBox(width: tokens.spacingStack.sm),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(bodyMeasures[i].name, style: titleStyle),
                              SizedBox(
                                height: tokens.spacingStack.xxs,
                              ),
                              Text(
                                bodyMeasures[i].measuringInstructions ?? '',
                                style: bodyStyle,
                              )
                            ],
                          ),
                        ),
                      ],
                    )
                  : Text('${i + 1}. ${bodyMeasures[i].name}',
                      style: titleStyle),
              SizedBox(height: tokens.spacingStack.xs),
              if (!theme.roundedNumerationStyle)
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: tokens.spacingStack.sm,
                  ),
                  child: Text(
                    bodyMeasures[i].measuringInstructions ?? '',
                    style: bodyStyle,
                  ),
                ),
            ],
          ),
          if (i < bodyMeasures.length - 1)
            SizedBox(height: tokens.spacingStack.md),
        ],
        SizedBox(height: tokens.spacingStack.sm),
      ],
    );
  }
}

class _MultipleSizeSelector extends StatelessWidget {
  final List<String> sizes;
  final ValueChanged<String> onSelect;
  final List<String?> selected;

  const _MultipleSizeSelector({
    required this.sizes,
    required this.onSelect,
    required this.selected,
  });

  @override
  Widget build(BuildContext context) {
    final tokens = context.designTokens;

    return SizedBox(
      width: double.infinity,
      child: Wrap(
        runSpacing: 8,
        spacing: 8,
        children: List.generate(sizes.length, (index) {
          final size = sizes[index];
          return SMSizeButton(
            textVariant: TextVariant.upperCase,
            text: TextUtils.verifySizeText(size),
            backgroundColor:
                selected.contains(size) ? tokens.colors.neutral.pure2 : null,
            height: 48,
            width: 72,
            textColor:
                selected.contains(size) ? tokens.colors.neutral.pure1 : null,
            onTap: () => onSelect(size),
          );
        }),
      ),
    );
  }
}

class _MultipeSizesTable extends StatefulWidget {
  final List<BodyMeasure> bodyMeasures;
  final List<SizeMeasures?> selectedSizeMeasures;
  final List<String?> selectedSizes;
  final double? rowHeight;
  final double? headerHeight;

  const _MultipeSizesTable({
    required this.bodyMeasures,
    required this.selectedSizeMeasures,
    required this.selectedSizes,
    this.rowHeight,
    this.headerHeight,
  });

  @override
  State<_MultipeSizesTable> createState() => _MultipeSizesTableState();
}

class _MultipeSizesTableState extends State<_MultipeSizesTable>
    with DesignTokensStateMixin {
  int? selectedColumn;
  int? selectedRow;

  @override
  Widget build(BuildContext context) {
    String columName(index) {
      return tokens.textTransform
          .body(widget.bodyMeasures[index].nameOnSizesTable);
    }

    String rowName(index, indexBodyMeasure) {
      return '${widget.selectedSizeMeasures[index]!.measures[widget.bodyMeasures[indexBodyMeasure].id]} cm';
    }

    return DefaultTextStyle(
      style: typography.typeStyles.body.copyWith(
        color: colors.typography.pure2,
        fontWeight: FontWeight.w500,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          width: MediaQuery.of(context).size.width *
              (widget.selectedSizes.length > 1
                  ? (widget.selectedSizes.length * 0.57)
                  : 1),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(),
            ),
            child: Table(
              border: TableBorder.all(
                color: colors.neutral.light1,
              ),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                _buildTableHeader(),
                for (int indexBodyMeasure = 0;
                    indexBodyMeasure < widget.bodyMeasures.length;
                    indexBodyMeasure++)
                  TableRow(
                    children: [
                      TableRowInkWell(
                        onTap: () {
                          setState(() {
                            selectedRow = indexBodyMeasure;
                          });
                        },
                        child: _buildCell(
                          height: 56,
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  columName(indexBodyMeasure),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          color: colors.neutral.light1,
                          textAlignment: Alignment.centerLeft,
                        ),
                      ),
                      for (int i = 0;
                          i < widget.selectedSizeMeasures.length;
                          i++)
                        TableRowInkWell(
                          onTap: () {
                            setState(() {
                              selectedColumn = i;
                              selectedRow = indexBodyMeasure;
                            });
                          },
                          child: _buildCell(
                            height: 56,
                            Text(
                              overflow: TextOverflow.ellipsis,
                              rowName(i, indexBodyMeasure),
                            ),
                            cellAlignment: TableCellVerticalAlignment.fill,
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  TableRow _buildTableHeader() {
    return TableRow(
      children: [
        _buildCell(
          Text('Medidas\ndo corpo',
              overflow: TextOverflow.ellipsis,
              style: typeStyles.subtitle.copyWith(
                fontWeight: FontWeight.w600,
              )),
          textAlignment: Alignment.centerLeft,
          height: widget.headerHeight,
          color: colors.neutral.light1,
        ),
        for (int i = 0; i < widget.selectedSizes.length; i++)
          _buildCell(
            Text(
              overflow: TextOverflow.ellipsis,
              'Tam. ${widget.selectedSizes[i]!}',
              style: typeStyles.body.copyWith(
                fontWeight: FontWeight.w600,
                color: colors.neutral.pure2,
              ),
            ),
            height: widget.headerHeight,
            color: colors.neutral.light1,
          ),
      ],
    );
  }

  Widget _buildCell(
    Widget content, {
    Alignment textAlignment = Alignment.center,
    Color? color,
    TableCellVerticalAlignment? cellAlignment,
    double? height,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: spacingInline.sm),
      constraints: BoxConstraints.tightFor(height: height ?? widget.rowHeight),
      decoration: BoxDecoration(
        color: color ?? Colors.transparent,
        border: Border.all(),
      ),
      alignment: textAlignment,
      child: content,
    );
  }
}
